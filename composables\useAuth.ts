import { ref } from "vue";
import { useRouter } from "vue-router";
import { useNuxtApp } from "#app";
import { useAuthStore } from "@/stores/auth";

export default function useAuth() {
  const { setOrgId, setStore } = usePermission();
  const $sdk = useNuxtApp().$sdk;
  const router = useRouter();
  const authStore = useAuthStore();
  const orgStore = useOrgStore();
  const username = ref("");
  const password = ref("");
  const hiddenPassword = ref(false);
  const notify = ref({ type: "", message: "" });
  const errors = ref({ username: "", password: "" });
  const loading = ref(false);
  // Define a configurable redirect path
  const defaultRedirectPath = "/org"; // Replace with your initial default
  const redirectPath = ref(defaultRedirectPath);

  const handleSubmit = async () => {
    // Reset errors
    //await $nextTick()
    await nextTick();
    errors.value = { username: "", password: "" };

    // Validate form fields
    if (!username.value.trim()) {
      errors.value.username = "Tài khoản không được để trống";
      return;
    }
    if (!password.value.trim()) {
      errors.value.password = "Mật khẩu không được để trống";
      return;
    }

    // Loading state
    loading.value = true;

    try {
      // Login API call
      const response = await $sdk.auth.login({
        username: username.value,
        password: password.value,
      });

      if (response) {
        orgStore.setOrg(response);
        useCookie("dataOrg").value = JSON.stringify(response.orgPositionsMap);
        setToken(response.accessToken);

        // Broadcast login success to other tabs
        if (process.client) {
          try {
            const { broadcastLoginSuccess } = useCrossTabBroadcast();
            broadcastLoginSuccess({
              token: response.accessToken,
              user: {
                id: response.partyId,
                name: response.fullName,
                email: response.email,
                phone: response.phone,
                avatar: response.avatarUrl,
                birthDate: response.birthDate,
              },
              orgData: response,
            });
          } catch (error) {
            console.warn("Failed to broadcast login success:", error);
          }
        }

        nextTick();
        console.log(
          "router.currentRoute.value.query",
          router.currentRoute.value.query
        );
        console.log(
          "router.currentRoute.value.query.path",
          router.currentRoute.value.query.path
        );
        if (router.currentRoute.value.query.orgId) {
          const user = {
            id: response.partyId,
            name: response.fullName,
            email: response.email,
            phone: response.phone,
            avatar: response.avatarUrl,
            birthDate: response.birthDate,
            roles:
              response.orgPositionsMap[
                router?.currentRoute?.value?.query?.orgId as any
              ],
          };

          setOrgId(router.currentRoute.value.query.orgId as string);
          setStore(router.currentRoute.value.query.storeId as string);

          await authStore.setUser(user);
          const query = router.currentRoute.value.query;
          const basePath = query.path || "/";

          const params = new URLSearchParams();

          if (query.orgId) params.append("orgId", String(query.orgId));
          if (query.storeId) params.append("storeId", String(query.storeId));
          if (query.orderId) params.append("orderId", String(query.orderId));
          if (query.customerId)
            params.append("customerId", String(query.customerId));

          const url = `${basePath}?${params.toString()}`;

          navigateTo(url);
          // router.push(url);
          return;
        } else {
          router.push(defaultRedirectPath);
        }
        const user = {
          id: response.partyId,
          name: response.fullName,
          email: response.email,
          phone: response.phone,
          avatar: response.avatarUrl,
          birthDate: response.birthDate,
        };
        authStore.setUser(user);
      }
    } catch (error) {
      useNuxtApp().$toast.error("Vui lòng kiểm tra lại tài khoản mật khẩu");

      console.error("Login error:", error);
    } finally {
      loading.value = false;
    }
  };
  const setToken = (token: string) => {
    const cookie = useCookie("token") as Ref<string>;
    cookie.value = token;
    authStore.setToken(token);
    $sdk.setToken(token);
  };

  const logout = async () => {
    // Clear cookies
    useCookie("token").value = null;
    useCookie("dataOrg").value = null;
    useCookie("storeId").value = "N/A";
    useCookie("orgId").value = "N/A";

    // Clear tab-isolated context
    const { clearContext } = useTabContext();
    clearContext();

    // Clear localStorage
    localStorage.clear();

    // Clear session storage for current tab
    sessionStorage.clear();

    authStore.setToken(null);
    authStore.setUser(null);
    router.push("/login");
  };

  const togglePasswordVisibility = () => {
    hiddenPassword.value = !hiddenPassword.value;
  };

  // Allow setting the redirect path from outside the composable
  const setRedirectPath = (newPath: string) => {
    redirectPath.value = newPath;
  };
  const checkToken = async (orgId: string, token: string) => {
    try {
      const response = await $sdk.authorization.checkToken(orgId, token);
      return response;
    } catch (error) {
      throw error;
    }
  };

  // Handle session expiry from other tabs (without broadcasting)
  const handleSessionExpired = async (reason?: string) => {
    // Clear cookies
    useCookie("token").value = null;
    useCookie("dataOrg").value = null;
    useCookie("storeId").value = "N/A";
    useCookie("orgId").value = "N/A";

    // Clear tab-isolated context
    const { clearContext } = useTabContext();
    clearContext();

    // Clear localStorage
    localStorage.clear();

    // Clear session storage for current tab
    sessionStorage.clear();

    authStore.setToken(null);
    authStore.setUser(null);

    // Don't redirect immediately - let the session expired modal handle it
    console.log("Session expired from another tab:", reason);
  };

  // Handle login success from other tabs (sync authentication state)
  const handleLoginSuccess = async (loginData: any) => {
    try {
      const { token, user, orgData } = loginData;

      if (!token || !user) {
        console.warn("Invalid login data received from another tab");
        return;
      }

      // Set authentication data
      setToken(token);
      authStore.setUser(user);

      if (orgData) {
        orgStore.setOrg(orgData);
        useCookie("dataOrg").value = JSON.stringify(orgData.orgPositionsMap);
      }

      console.log("Authentication state synchronized from another tab");

      // Optionally show a toast notification
      if (process.client) {
        useNuxtApp().$toast.success("Đã đăng nhập thành công từ tab khác");
      }
    } catch (error) {
      console.error("Failed to handle login success from another tab:", error);
    }
  };
  return {
    username,
    password,
    hiddenPassword,
    notify,
    errors,
    loading,
    handleSubmit,
    togglePasswordVisibility,
    setRedirectPath,
    logout,
    setToken,
    checkToken,
    handleSessionExpired,
    handleLoginSuccess,
  };
}
