<template>
  <div
    v-if="isOpen"
    class="fixed inset-0 z-[9999] flex items-center justify-center bg-black bg-opacity-50"
    @click.self="handleOverlayClick"
  >
    <div
      class="bg-white rounded-lg shadow-lg w-[90%] max-w-md mx-auto relative"
      @click.stop
    >
      <!-- <PERSON><PERSON> -->
      <div class="flex items-center justify-between p-6 border-b border-gray-200">
        <div class="flex items-center space-x-3">
          <!-- Warning Icon -->
          <div class="flex-shrink-0">
            <svg
              class="w-8 h-8 text-red-500"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"
              />
            </svg>
          </div>
          <h2 class="text-xl font-bold text-gray-900">Phiên đăng nhập hết hạn</h2>
        </div>
        
        <!-- Close button (optional, can be disabled for forced action) -->
        <button
          v-if="allowClose"
          @click="handleClose"
          class="text-gray-400 hover:text-gray-600 transition-colors"
          aria-label="Đóng"
        >
          <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
      </div>

      <!-- Modal Body -->
      <div class="p-6">
        <div class="text-center">
          <div class="mb-4">
            <p class="text-gray-800 text-base leading-relaxed mb-2">
              {{ getMainMessage() }}
            </p>
            <p class="text-gray-600 text-sm">
              {{ getSubMessage() }}
            </p>
          </div>
          
          <div v-if="showDetails && reason" class="bg-gray-50 rounded-lg p-3 mb-4">
            <p class="text-sm text-gray-600">
              <span class="font-medium">Lý do:</span> {{ getReasonText() }}
            </p>
          </div>

          <!-- Auto redirect countdown with progress -->
          <div v-if="autoRedirect && countdown > 0" class="mb-4">
            <div class="bg-blue-50 rounded-lg p-3 border border-blue-200">
              <p class="text-blue-800 text-sm font-medium mb-2">
                Tự động chuyển hướng sau {{ countdown }} giây
              </p>
              <div class="w-full bg-blue-200 rounded-full h-2">
                <div 
                  class="bg-blue-600 h-2 rounded-full transition-all duration-1000 ease-linear"
                  :style="{ width: `${((autoRedirectDelay - countdown) / autoRedirectDelay) * 100}%` }"
                ></div>
              </div>
            </div>
          </div>

          <!-- Security notice -->
          <div class="bg-amber-50 rounded-lg p-3 border border-amber-200">
            <div class="flex items-start space-x-2">
              <svg class="w-5 h-5 text-amber-600 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
              </svg>
              <p class="text-amber-800 text-xs">
                Để bảo mật tài khoản, vui lòng đăng nhập lại khi phiên làm việc hết hạn.
              </p>
            </div>
          </div>
        </div>
      </div>

      <!-- Modal Footer -->
      <div class="flex justify-center space-x-4 p-6 border-t border-gray-200">
        <button
          @click="handleLoginRedirect"
          class="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-medium focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
          :disabled="isRedirecting"
        >
          <span v-if="isRedirecting" class="flex items-center">
            <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
              <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            Đang chuyển hướng...
          </span>
          <span v-else>Đăng nhập lại</span>
        </button>
        
        <button
          v-if="allowDismiss && !autoRedirect"
          @click="handleDismiss"
          class="px-6 py-2 bg-gray-300 text-gray-700 rounded-lg hover:bg-gray-400 transition-colors font-medium focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2"
          :disabled="isRedirecting"
        >
          Để sau
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch, computed } from "vue";
import { useRouter } from "vue-router";

interface Props {
  isOpen: boolean;
  message?: string;
  reason?: string;
  allowClose?: boolean;
  allowDismiss?: boolean;
  autoRedirect?: boolean;
  autoRedirectDelay?: number;
  showDetails?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  isOpen: false,
  allowClose: false,
  allowDismiss: false,
  autoRedirect: true,
  autoRedirectDelay: 10,
  showDetails: false
});

const emit = defineEmits<{
  close: [];
  dismiss: [];
  loginRedirect: [];
}>();

const router = useRouter();
const isRedirecting = ref(false);
const countdown = ref(props.autoRedirectDelay);
let countdownInterval: NodeJS.Timeout | null = null;

// Computed messages for better UX
const getMainMessage = () => {
  if (props.message) return props.message;
  
  const reasonType = props.reason?.toLowerCase() || '';
  
  if (reasonType.includes('logout') || reasonType.includes('user_initiated')) {
    return 'Phiên đăng nhập đã kết thúc';
  } else if (reasonType.includes('expired') || reasonType.includes('token')) {
    return 'Phiên đăng nhập hết hạn';
  } else {
    return 'Cần đăng nhập lại';
  }
};

const getSubMessage = () => {
  const reasonType = props.reason?.toLowerCase() || '';
  
  if (reasonType.includes('logout') || reasonType.includes('user_initiated')) {
    return 'Bạn đã đăng xuất từ tab khác. Vui lòng đăng nhập lại để tiếp tục.';
  } else if (reasonType.includes('expired')) {
    return 'Phiên làm việc đã hết hạn vì lý do bảo mật. Vui lòng đăng nhập lại.';
  } else {
    return 'Để tiếp tục sử dụng hệ thống, vui lòng đăng nhập lại.';
  }
};

const getReasonText = () => {
  const reasonMap: Record<string, string> = {
    'user_initiated_logout': 'Đăng xuất từ tab khác',
    'logout_from_another_tab': 'Đăng xuất từ tab khác',
    'token_expired': 'Token hết hạn',
    'session_expired': 'Phiên làm việc hết hạn',
    'unauthorized': 'Không có quyền truy cập'
  };
  
  return reasonMap[props.reason || ''] || props.reason || 'Không xác định';
};

// Start countdown when modal opens
const startCountdown = () => {
  if (!props.autoRedirect) return;
  
  countdown.value = props.autoRedirectDelay;
  countdownInterval = setInterval(() => {
    countdown.value--;
    if (countdown.value <= 0) {
      handleLoginRedirect();
    }
  }, 1000);
};

// Stop countdown
const stopCountdown = () => {
  if (countdownInterval) {
    clearInterval(countdownInterval);
    countdownInterval = null;
  }
};

// Handle overlay click
const handleOverlayClick = () => {
  if (props.allowClose) {
    handleClose();
  }
};

// Handle close button
const handleClose = () => {
  stopCountdown();
  emit('close');
};

// Handle dismiss button
const handleDismiss = () => {
  stopCountdown();
  emit('dismiss');
};

// Handle login redirect
const handleLoginRedirect = async () => {
  if (isRedirecting.value) return;
  
  stopCountdown();
  isRedirecting.value = true;
  
  try {
    emit('loginRedirect');
    
    // Build login URL with current path as redirect
    const currentPath = router.currentRoute.value.fullPath;
    const loginQuery: Record<string, string> = {};
    
    // Only add path if it's not root or login page (following user preference)
    if (currentPath !== '/' && currentPath !== '/login') {
      loginQuery.path = currentPath;
    }
    
    // Preserve important query parameters
    const currentQuery = router.currentRoute.value.query;
    if (currentQuery.orgId) loginQuery.orgId = String(currentQuery.orgId);
    if (currentQuery.storeId) loginQuery.storeId = String(currentQuery.storeId);
    if (currentQuery.orderId) loginQuery.orderId = String(currentQuery.orderId);
    if (currentQuery.customerId) loginQuery.customerId = String(currentQuery.customerId);
    
    await router.push({ path: '/login', query: loginQuery });
  } catch (error) {
    console.error('Failed to redirect to login:', error);
    isRedirecting.value = false;
  }
};

// Watch for modal open state changes
watch(() => props.isOpen, (newValue) => {
  if (newValue) {
    startCountdown();
  } else {
    stopCountdown();
  }
});

// Cleanup on unmount
onUnmounted(() => {
  stopCountdown();
});

// Handle keyboard events
const handleKeydown = (event: KeyboardEvent) => {
  if (!props.isOpen) return;
  
  if (event.key === 'Escape' && props.allowClose) {
    handleClose();
  } else if (event.key === 'Enter') {
    handleLoginRedirect();
  }
};

onMounted(() => {
  document.addEventListener('keydown', handleKeydown);
});

onUnmounted(() => {
  document.removeEventListener('keydown', handleKeydown);
});
</script>

<style scoped>
/* Ensure modal appears above all other content */
.z-\[9999\] {
  z-index: 9999;
}

/* Smooth transitions */
.transition-colors {
  transition-property: color, background-color, border-color;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

/* Loading spinner animation */
@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.animate-spin {
  animation: spin 1s linear infinite;
}

/* Focus styles for accessibility */
button:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

/* Responsive adjustments */
@media (max-width: 640px) {
  .w-\[90\%\] {
    width: 95%;
  }
}
</style>
