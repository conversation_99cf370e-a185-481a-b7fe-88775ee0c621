/**
 * Comprehensive Test Suite for Cross-Tab Broadcast Functionality
 * 
 * This file contains helper functions to test the cross-tab broadcast
 * mechanism in development or testing environments.
 */

interface TestResult {
  success: boolean;
  message: string;
  timestamp: number;
}

interface TestSuite {
  results: TestResult[];
  startTime: number;
  endTime?: number;
}

export const testCrossTabBroadcast = () => {
  if (!process.client) {
    console.warn('Cross-tab broadcast test can only run on client side');
    return;
  }

  const { 
    initializeBroadcast, 
    broadcastSessionExpired, 
    broadcastLogout,
    broadcastLoginSuccess,
    broadcastAuthStateSync,
    onSessionExpired,
    onLogout,
    onLoginSuccess,
    onAuthStateSync,
    MESSAGE_TYPES
  } = useCrossTabBroadcast({ enableLogging: true });

  // Test suite state
  const testSuite: TestSuite = {
    results: [],
    startTime: Date.now()
  };

  // Initialize broadcast
  const isSupported = initializeBroadcast();
  
  if (!isSupported) {
    console.error('BroadcastChannel is not supported in this browser');
    return;
  }

  console.log('🚀 Cross-tab broadcast test suite initialized');

  // Helper function to add test result
  const addTestResult = (success: boolean, message: string) => {
    const result: TestResult = {
      success,
      message,
      timestamp: Date.now()
    };
    testSuite.results.push(result);
    console.log(success ? '✅' : '❌', message);
  };

  // Test functions
  const testSessionExpired = (reason = 'test_session_expired') => {
    console.log('🧪 Testing session expired broadcast...');
    const success = broadcastSessionExpired(reason);
    addTestResult(success, `Session expired broadcast ${success ? 'sent' : 'failed'}`);
    return success;
  };

  const testLogout = (reason = 'test_logout') => {
    console.log('🧪 Testing logout broadcast...');
    const success = broadcastLogout(reason);
    addTestResult(success, `Logout broadcast ${success ? 'sent' : 'failed'}`);
    return success;
  };

  const testLoginSuccess = () => {
    console.log('🧪 Testing login success broadcast...');
    const mockAuthData = {
      token: 'test_token_' + Date.now(),
      user: {
        id: 'test_user',
        name: 'Test User',
        email: '<EMAIL>'
      }
    };
    const success = broadcastLoginSuccess(mockAuthData);
    addTestResult(success, `Login success broadcast ${success ? 'sent' : 'failed'}`);
    return success;
  };

  const testAuthStateSync = () => {
    console.log('🧪 Testing auth state sync broadcast...');
    const mockAuthState = {
      isAuthenticated: true,
      timestamp: Date.now()
    };
    const success = broadcastAuthStateSync(mockAuthState);
    addTestResult(success, `Auth state sync broadcast ${success ? 'sent' : 'failed'}`);
    return success;
  };

  // Comprehensive test runner
  const runAllTests = async () => {
    console.log('🏃‍♂️ Running comprehensive cross-tab broadcast tests...');
    
    testSessionExpired();
    await new Promise(resolve => setTimeout(resolve, 500));
    
    testLogout();
    await new Promise(resolve => setTimeout(resolve, 500));
    
    testLoginSuccess();
    await new Promise(resolve => setTimeout(resolve, 500));
    
    testAuthStateSync();
    
    testSuite.endTime = Date.now();
    const duration = testSuite.endTime - testSuite.startTime;
    const successCount = testSuite.results.filter(r => r.success).length;
    const totalCount = testSuite.results.length;
    
    console.log(`📊 Test Results: ${successCount}/${totalCount} passed in ${duration}ms`);
    return testSuite;
  };

  // Listen for events (for testing in same tab)
  const unsubscribeSessionExpired = onSessionExpired((message) => {
    console.log('📨 Received session expired event:', message);
    addTestResult(true, 'Session expired event received');
  });

  const unsubscribeLogout = onLogout((message) => {
    console.log('📨 Received logout event:', message);
    addTestResult(true, 'Logout event received');
  });

  const unsubscribeLoginSuccess = onLoginSuccess((message) => {
    console.log('📨 Received login success event:', message);
    addTestResult(true, 'Login success event received');
  });

  const unsubscribeAuthStateSync = onAuthStateSync((message) => {
    console.log('📨 Received auth state sync event:', message);
    addTestResult(true, 'Auth state sync event received');
  });

  // Cleanup function
  const cleanup = () => {
    unsubscribeSessionExpired();
    unsubscribeLogout();
    unsubscribeLoginSuccess();
    unsubscribeAuthStateSync();
    console.log('🧹 Test cleanup completed');
  };

  // Performance test
  const performanceTest = () => {
    console.log('⚡ Running performance test...');
    const iterations = 100;
    const startTime = performance.now();
    
    for (let i = 0; i < iterations; i++) {
      broadcastSessionExpired(`perf_test_${i}`);
    }
    
    const endTime = performance.now();
    const duration = endTime - startTime;
    const avgTime = duration / iterations;
    
    console.log(`📈 Performance: ${iterations} broadcasts in ${duration.toFixed(2)}ms (avg: ${avgTime.toFixed(2)}ms per broadcast)`);
    addTestResult(true, `Performance test completed: ${avgTime.toFixed(2)}ms avg`);
  };

  // Stress test
  const stressTest = () => {
    console.log('💪 Running stress test...');
    const types = ['SESSION_EXPIRED', 'LOGOUT', 'LOGIN_SUCCESS', 'AUTH_STATE_SYNC'] as const;
    
    for (let i = 0; i < 50; i++) {
      const randomType = types[Math.floor(Math.random() * types.length)];
      switch (randomType) {
        case 'SESSION_EXPIRED':
          broadcastSessionExpired(`stress_test_${i}`);
          break;
        case 'LOGOUT':
          broadcastLogout(`stress_test_${i}`);
          break;
        case 'LOGIN_SUCCESS':
          broadcastLoginSuccess({ token: `stress_${i}`, user: { id: `user_${i}` } });
          break;
        case 'AUTH_STATE_SYNC':
          broadcastAuthStateSync({ test: true, iteration: i });
          break;
      }
    }
    
    addTestResult(true, 'Stress test completed: 50 random broadcasts sent');
  };

  // Expose test functions to window for manual testing
  if (typeof window !== 'undefined') {
    (window as any).testCrossTab = {
      // Individual tests
      testSessionExpired,
      testLogout,
      testLoginSuccess,
      testAuthStateSync,
      
      // Test suites
      runAllTests,
      performanceTest,
      stressTest,
      
      // Utilities
      cleanup,
      getResults: () => testSuite,
      MESSAGE_TYPES,
      
      // Real auth simulation
      simulateRealLogout: () => {
        const { logout } = useAuth();
        logout();
      },
      
      simulateRealLogin: async () => {
        console.log('⚠️  This would require actual login credentials');
        console.log('Use the real login form instead');
      }
    };

    console.log('🎮 Test functions exposed to window.testCrossTab');
    console.log('Available functions:');
    console.log('- window.testCrossTab.testSessionExpired()');
    console.log('- window.testCrossTab.testLogout()');
    console.log('- window.testCrossTab.testLoginSuccess()');
    console.log('- window.testCrossTab.testAuthStateSync()');
    console.log('- window.testCrossTab.runAllTests()');
    console.log('- window.testCrossTab.performanceTest()');
    console.log('- window.testCrossTab.stressTest()');
    console.log('- window.testCrossTab.simulateRealLogout()');
    console.log('- window.testCrossTab.cleanup()');
    console.log('- window.testCrossTab.getResults()');
  }

  return {
    testSessionExpired,
    testLogout,
    testLoginSuccess,
    testAuthStateSync,
    runAllTests,
    performanceTest,
    stressTest,
    cleanup,
    getResults: () => testSuite
  };
};

/**
 * Automated Test Instructions:
 * 
 * 1. Open multiple tabs of the application
 * 2. In the browser console of one tab, run:
 *    - testCrossTabBroadcast()
 *    - window.testCrossTab.runAllTests()
 * 
 * 3. Check other tabs to see if events are received
 * 
 * 4. Test real scenarios:
 *    - window.testCrossTab.simulateRealLogout()
 *    - Use actual login form in another tab
 * 
 * 5. Performance testing:
 *    - window.testCrossTab.performanceTest()
 *    - window.testCrossTab.stressTest()
 * 
 * 6. Check results:
 *    - window.testCrossTab.getResults()
 * 
 * 7. Clean up:
 *    - window.testCrossTab.cleanup()
 */
