/**
 * Session Monitor Plugin
 * 
 * This plugin initializes cross-tab session monitoring and manages
 * the session expired modal globally across the application.
 */

import { ref } from 'vue';

export default defineNuxtPlugin(() => {
  // Only run on client side
  if (!process.client) return;

  // Global state for session expired modal
  const isSessionExpiredModalOpen = ref(false);
  const sessionExpiredReason = ref<string>('');

  // Initialize broadcast system
  const { 
    initializeBroadcast, 
    onSessionExpired, 
    onLogout,
    onLoginSuccess,
    getSessionExpiredModalState,
    setSessionExpiredModalState,
    resetSessionExpiredModal
  } = useCrossTabBroadcast({ enableLogging: false });

  // Initialize the broadcast channel
  const isBroadcastSupported = initializeBroadcast();
  
  if (!isBroadcastSupported) {
    console.warn('Cross-tab session monitoring is not available in this browser');
    return;
  }

  // Get auth functions
  const { handleSessionExpired, handleLoginSuccess } = useAuth();

  // Handle session expired events from other tabs
  const handleSessionExpiredEvent = async (message: any) => {
    // Prevent multiple modals from showing
    if (getSessionExpiredModalState()) {
      console.log('Session expired modal already shown, ignoring duplicate event');
      return;
    }

    console.log('Received session expired event from another tab:', message);
    
    // Set modal state to prevent duplicates
    setSessionExpiredModalState(true);
    
    // Clear session data without broadcasting (to avoid loops)
    await handleSessionExpired(message.data?.reason || 'logout_from_another_tab');
    
    // Show the modal
    sessionExpiredReason.value = message.data?.reason || 'Đăng xuất từ tab khác';
    isSessionExpiredModalOpen.value = true;
  };

  // Handle logout events from other tabs (same as session expired)
  const handleLogoutEvent = async (message: any) => {
    await handleSessionExpiredEvent(message);
  };

  // Handle login success events from other tabs
  const handleLoginSuccessEvent = async (message: any) => {
    console.log('Received login success event from another tab:', message);
    
    // Only sync if current tab is not authenticated
    const authStore = useAuthStore();
    if (!authStore.token) {
      await handleLoginSuccess(message.data?.authData);
    }
  };

  // Register event listeners
  const unsubscribeSessionExpired = onSessionExpired(handleSessionExpiredEvent);
  const unsubscribeLogout = onLogout(handleLogoutEvent);
  const unsubscribeLoginSuccess = onLoginSuccess(handleLoginSuccessEvent);

  // Modal event handlers
  const handleModalClose = () => {
    isSessionExpiredModalOpen.value = false;
    resetSessionExpiredModal();
  };

  const handleModalDismiss = () => {
    isSessionExpiredModalOpen.value = false;
    resetSessionExpiredModal();
  };

  const handleModalLoginRedirect = () => {
    isSessionExpiredModalOpen.value = false;
    resetSessionExpiredModal();
    // Navigation is handled by the modal component
  };

  // Handle page visibility changes to prevent stale modals
  const handleVisibilityChange = () => {
    if (document.hidden) {
      // Tab is hidden, don't show modals
      return;
    }
    
    // Tab is visible, check for deferred messages
    const { handleDeferredMessages } = useCrossTabBroadcast();
    handleDeferredMessages();
  };

  // Handle beforeunload to cleanup
  const handleBeforeUnload = () => {
    // Cleanup is handled by the composable
    unsubscribeSessionExpired();
    unsubscribeLogout();
    unsubscribeLoginSuccess();
  };

  // Add event listeners
  document.addEventListener('visibilitychange', handleVisibilityChange);
  window.addEventListener('beforeunload', handleBeforeUnload);

  // Provide global access to modal state and handlers
  return {
    provide: {
      sessionMonitor: {
        isSessionExpiredModalOpen: readonly(isSessionExpiredModalOpen),
        sessionExpiredReason: readonly(sessionExpiredReason),
        handleModalClose,
        handleModalDismiss,
        handleModalLoginRedirect,
        isBroadcastSupported
      }
    }
  };
});
