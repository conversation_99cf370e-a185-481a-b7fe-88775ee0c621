/**
 * Cross-Tab Broadcast Communication
 *
 * This composable provides cross-tab communication using the BroadcastChannel API
 * to handle session expiry notifications and other cross-tab events.
 */

import { ref, onUnmounted } from "vue";

interface BroadcastMessage {
  type: "SESSION_EXPIRED" | "LOGOUT" | "SESSION_REFRESH";
  timestamp: number;
  tabId?: string;
  data?: any;
}

interface BroadcastOptions {
  channelName?: string;
  enableLogging?: boolean;
}

const CHANNEL_NAME = "dms-app-broadcast";
const MESSAGE_TYPES = {
  SESSION_EXPIRED: "SESSION_EXPIRED",
  LOGOUT: "LOGOUT",
  SESSION_REFRESH: "SESSION_REFRESH",
} as const;

// Global state to track if session expired modal is already shown
const isSessionExpiredModalShown = ref(false);
const lastBroadcastTime = ref(0);
const messageHistory = new Set<string>();
const MAX_HISTORY_SIZE = 100;

export default function useCrossTabBroadcast(options: BroadcastOptions = {}) {
  const { channelName = CHANNEL_NAME, enableLogging = false } = options;

  let broadcastChannel: BroadcastChannel | null = null;
  const listeners = new Map<string, Function[]>();

  // Initialize BroadcastChannel if supported
  const initializeBroadcast = (): boolean => {
    if (typeof BroadcastChannel === "undefined") {
      console.warn("BroadcastChannel API is not supported in this browser");
      return false;
    }

    try {
      broadcastChannel = new BroadcastChannel(channelName);

      broadcastChannel.addEventListener("message", (event) => {
        handleBroadcastMessage(event.data);
      });

      if (enableLogging) {
        console.log(`BroadcastChannel initialized: ${channelName}`);
      }

      return true;
    } catch (error) {
      console.error("Failed to initialize BroadcastChannel:", error);
      return false;
    }
  };

  // Handle incoming broadcast messages
  const handleBroadcastMessage = (message: BroadcastMessage) => {
    if (!message || typeof message !== "object") return;

    const { type, timestamp, tabId, data } = message;

    // Create unique message ID for deduplication
    const messageId = `${type}-${timestamp}-${tabId}`;

    // Prevent processing duplicate messages
    if (messageHistory.has(messageId)) {
      if (enableLogging) {
        console.log("Ignoring duplicate message:", messageId);
      }
      return;
    }

    // Prevent processing old messages
    if (timestamp <= lastBroadcastTime.value) return;

    // Get current tab ID to avoid self-messages
    const { tabId: currentTabId } = useTabContext();
    if (tabId === currentTabId.value) return;

    // Add to message history
    messageHistory.add(messageId);

    // Limit history size to prevent memory leaks
    if (messageHistory.size > MAX_HISTORY_SIZE) {
      const firstItem = messageHistory.values().next().value;
      if (firstItem) {
        messageHistory.delete(firstItem);
      }
    }

    if (enableLogging) {
      console.log("Received broadcast message:", {
        type,
        timestamp,
        tabId,
        data,
      });
    }

    // Update last broadcast time
    lastBroadcastTime.value = timestamp;

    // For session expired messages, add additional checks
    if (
      type === MESSAGE_TYPES.SESSION_EXPIRED ||
      type === MESSAGE_TYPES.LOGOUT
    ) {
      // Check if modal is already shown to prevent multiple popups
      if (isSessionExpiredModalShown.value) {
        if (enableLogging) {
          console.log("Session expired modal already shown, ignoring message");
        }
        return;
      }

      // Check document visibility to avoid showing modal in background tabs
      if (document.hidden) {
        if (enableLogging) {
          console.log("Tab is hidden, deferring session expired modal");
        }
        // Store the message to show when tab becomes visible
        sessionStorage.setItem(
          "dms_deferred_session_expired",
          JSON.stringify(message)
        );
        return;
      }
    }

    // Trigger registered listeners
    const typeListeners = listeners.get(type) || [];
    typeListeners.forEach((listener) => {
      try {
        listener(message);
      } catch (error) {
        console.error("Error in broadcast listener:", error);
      }
    });
  };

  // Send broadcast message to other tabs
  const broadcast = (type: keyof typeof MESSAGE_TYPES, data?: any): boolean => {
    if (!broadcastChannel) {
      console.warn("BroadcastChannel not initialized");
      return false;
    }

    const { tabId } = useTabContext();
    const message: BroadcastMessage = {
      type: MESSAGE_TYPES[type],
      timestamp: Date.now(),
      tabId: tabId.value,
      data,
    };

    try {
      broadcastChannel.postMessage(message);

      if (enableLogging) {
        console.log("Broadcast message sent:", message);
      }

      return true;
    } catch (error) {
      console.error("Failed to send broadcast message:", error);
      return false;
    }
  };

  // Register listener for specific message type
  const onBroadcast = (
    type: keyof typeof MESSAGE_TYPES,
    callback: (message: BroadcastMessage) => void
  ) => {
    const messageType = MESSAGE_TYPES[type];

    if (!listeners.has(messageType)) {
      listeners.set(messageType, []);
    }

    listeners.get(messageType)!.push(callback);

    // Return unsubscribe function
    return () => {
      const typeListeners = listeners.get(messageType);
      if (typeListeners) {
        const index = typeListeners.indexOf(callback);
        if (index > -1) {
          typeListeners.splice(index, 1);
        }
      }
    };
  };

  // Broadcast session expired event
  const broadcastSessionExpired = (reason?: string) => {
    return broadcast("SESSION_EXPIRED", { reason });
  };

  // Broadcast logout event
  const broadcastLogout = (reason?: string) => {
    return broadcast("LOGOUT", { reason });
  };

  // Broadcast session refresh event
  const broadcastSessionRefresh = () => {
    return broadcast("SESSION_REFRESH");
  };

  // Listen for session expired events
  const onSessionExpired = (callback: (message: BroadcastMessage) => void) => {
    return onBroadcast("SESSION_EXPIRED", callback);
  };

  // Listen for logout events
  const onLogout = (callback: (message: BroadcastMessage) => void) => {
    return onBroadcast("LOGOUT", callback);
  };

  // Listen for session refresh events
  const onSessionRefresh = (callback: (message: BroadcastMessage) => void) => {
    return onBroadcast("SESSION_REFRESH", callback);
  };

  // Reset session expired modal state
  const resetSessionExpiredModal = () => {
    isSessionExpiredModalShown.value = false;
  };

  // Check if session expired modal is already shown
  const getSessionExpiredModalState = () => {
    return isSessionExpiredModalShown.value;
  };

  // Set session expired modal state
  const setSessionExpiredModalState = (shown: boolean) => {
    isSessionExpiredModalShown.value = shown;
  };

  // Check for and handle deferred session expired messages
  const handleDeferredMessages = () => {
    const deferredMessage = sessionStorage.getItem(
      "dms_deferred_session_expired"
    );
    if (deferredMessage) {
      try {
        const message = JSON.parse(deferredMessage);
        sessionStorage.removeItem("dms_deferred_session_expired");

        // Process the deferred message
        handleBroadcastMessage(message);
      } catch (error) {
        console.error(
          "Failed to process deferred session expired message:",
          error
        );
        sessionStorage.removeItem("dms_deferred_session_expired");
      }
    }
  };

  // Cleanup function
  const cleanup = () => {
    if (broadcastChannel) {
      broadcastChannel.close();
      broadcastChannel = null;
    }
    listeners.clear();

    if (enableLogging) {
      console.log("BroadcastChannel cleanup completed");
    }
  };

  // Auto cleanup on unmount
  onUnmounted(() => {
    cleanup();
  });

  return {
    // Core functions
    initializeBroadcast,
    broadcast,
    onBroadcast,
    cleanup,

    // Specific event functions
    broadcastSessionExpired,
    broadcastLogout,
    broadcastSessionRefresh,
    onSessionExpired,
    onLogout,
    onSessionRefresh,

    // Modal state management
    resetSessionExpiredModal,
    getSessionExpiredModalState,
    setSessionExpiredModalState,
    handleDeferredMessages,

    // State
    isSessionExpiredModalShown: readonly(isSessionExpiredModalShown),

    // Constants
    MESSAGE_TYPES,
  };
}
