/**
 * Cross-Tab Broadcast Communication
 *
 * This composable provides cross-tab communication using the BroadcastChannel API
 * to handle session expiry notifications and other cross-tab events.
 */

import { ref, onUnmounted } from "vue";

interface BroadcastMessage {
  type:
    | "SESSION_EXPIRED"
    | "LOGOUT"
    | "SESSION_REFRESH"
    | "LOGIN_SUCCESS"
    | "AUTH_STATE_SYNC";
  timestamp: number;
  tabId?: string;
  data?: any;
}

interface BroadcastOptions {
  channelName?: string;
  enableLogging?: boolean;
}

const CHANNEL_NAME = "dms-app-broadcast";
const MESSAGE_TYPES = {
  SESSION_EXPIRED: "SESSION_EXPIRED",
  LOGOUT: "LOGOUT",
  SESSION_REFRESH: "SESSION_REFRESH",
  LOGIN_SUCCESS: "LOGIN_SUCCESS",
  AUTH_STATE_SYNC: "AUTH_STATE_SYNC",
} as const;

// Global state to track if session expired modal is already shown
const isSessionExpiredModalShown = ref(false);
const lastBroadcastTime = ref(0);
const messageHistory = new Set<string>();
const MAX_HISTORY_SIZE = 100;

// Performance monitoring
const performanceMetrics = {
  messagesSent: 0,
  messagesReceived: 0,
  messagesDropped: 0,
  lastCleanup: Date.now(),
  averageProcessingTime: 0,
  processingTimes: [] as number[],
};

export default function useCrossTabBroadcast(options: BroadcastOptions = {}) {
  const { channelName = CHANNEL_NAME, enableLogging = false } = options;

  let broadcastChannel: BroadcastChannel | null = null;
  const listeners = new Map<string, Function[]>();
  let fallbackMode = false;
  const FALLBACK_STORAGE_KEY = "dms_cross_tab_fallback";

  // Initialize BroadcastChannel if supported, fallback to localStorage
  const initializeBroadcast = (): boolean => {
    if (typeof BroadcastChannel === "undefined") {
      console.warn(
        "BroadcastChannel API is not supported, using localStorage fallback"
      );
      return initializeFallbackMode();
    }

    try {
      broadcastChannel = new BroadcastChannel(channelName);

      broadcastChannel.addEventListener("message", (event) => {
        handleBroadcastMessage(event.data);
      });

      if (enableLogging) {
        console.log(`BroadcastChannel initialized: ${channelName}`);
      }

      return true;
    } catch (error) {
      console.error(
        "Failed to initialize BroadcastChannel, using fallback:",
        error
      );
      return initializeFallbackMode();
    }
  };

  // Initialize fallback mode using localStorage events
  const initializeFallbackMode = (): boolean => {
    if (typeof localStorage === "undefined") {
      console.error("Neither BroadcastChannel nor localStorage is available");
      return false;
    }

    fallbackMode = true;

    // Listen for localStorage changes from other tabs
    const handleStorageEvent = (event: StorageEvent) => {
      if (event.key === FALLBACK_STORAGE_KEY && event.newValue) {
        try {
          const message = JSON.parse(event.newValue);
          handleBroadcastMessage(message);
        } catch (error) {
          console.error("Failed to parse fallback message:", error);
        }
      }
    };

    window.addEventListener("storage", handleStorageEvent);

    if (enableLogging) {
      console.log("Fallback mode initialized using localStorage");
    }

    return true;
  };

  // Handle incoming broadcast messages
  const handleBroadcastMessage = (message: BroadcastMessage) => {
    const startTime = performance.now();

    if (!message || typeof message !== "object") {
      performanceMetrics.messagesDropped++;
      return;
    }

    const { type, timestamp, tabId, data } = message;

    // Create unique message ID for deduplication
    const messageId = `${type}-${timestamp}-${tabId}`;

    // Prevent processing duplicate messages
    if (messageHistory.has(messageId)) {
      performanceMetrics.messagesDropped++;
      if (enableLogging) {
        console.log("Ignoring duplicate message:", messageId);
      }
      return;
    }

    // Prevent processing old messages
    if (timestamp <= lastBroadcastTime.value) {
      performanceMetrics.messagesDropped++;
      return;
    }

    // Get current tab ID to avoid self-messages
    const { tabId: currentTabId } = useTabContext();
    if (tabId === currentTabId.value) {
      performanceMetrics.messagesDropped++;
      return;
    }

    // Add to message history
    messageHistory.add(messageId);

    // Limit history size to prevent memory leaks
    if (messageHistory.size > MAX_HISTORY_SIZE) {
      const firstItem = messageHistory.values().next().value;
      if (firstItem) {
        messageHistory.delete(firstItem);
      }
    }

    // Periodic cleanup of performance metrics
    const now = Date.now();
    if (now - performanceMetrics.lastCleanup > 60000) {
      // Every minute
      performanceMetrics.processingTimes =
        performanceMetrics.processingTimes.slice(-100); // Keep last 100
      performanceMetrics.lastCleanup = now;
    }

    if (enableLogging) {
      console.log("Received broadcast message:", {
        type,
        timestamp,
        tabId,
        data,
      });
    }

    // Update last broadcast time
    lastBroadcastTime.value = timestamp;

    // For session expired messages, add additional checks
    if (
      type === MESSAGE_TYPES.SESSION_EXPIRED ||
      type === MESSAGE_TYPES.LOGOUT
    ) {
      // Check if modal is already shown to prevent multiple popups
      if (isSessionExpiredModalShown.value) {
        performanceMetrics.messagesDropped++;
        if (enableLogging) {
          console.log("Session expired modal already shown, ignoring message");
        }
        return;
      }

      // Check document visibility to avoid showing modal in background tabs
      if (document.hidden) {
        if (enableLogging) {
          console.log("Tab is hidden, deferring session expired modal");
        }
        // Store the message to show when tab becomes visible
        sessionStorage.setItem(
          "dms_deferred_session_expired",
          JSON.stringify(message)
        );
        return;
      }
    }

    // Trigger registered listeners
    const typeListeners = listeners.get(type) || [];
    typeListeners.forEach((listener) => {
      try {
        listener(message);
      } catch (error) {
        console.error("Error in broadcast listener:", error);
      }
    });

    // Update performance metrics
    const processingTime = performance.now() - startTime;
    performanceMetrics.messagesReceived++;
    performanceMetrics.processingTimes.push(processingTime);

    // Calculate rolling average
    if (performanceMetrics.processingTimes.length > 0) {
      const sum = performanceMetrics.processingTimes.reduce((a, b) => a + b, 0);
      performanceMetrics.averageProcessingTime =
        sum / performanceMetrics.processingTimes.length;
    }
  };

  // Send broadcast message to other tabs
  const broadcast = (type: keyof typeof MESSAGE_TYPES, data?: any): boolean => {
    const { tabId } = useTabContext();
    const message: BroadcastMessage = {
      type: MESSAGE_TYPES[type],
      timestamp: Date.now(),
      tabId: tabId.value,
      data,
    };

    try {
      if (fallbackMode) {
        // Use localStorage fallback
        localStorage.setItem(FALLBACK_STORAGE_KEY, JSON.stringify(message));
        // Clear immediately to trigger storage event
        setTimeout(() => {
          localStorage.removeItem(FALLBACK_STORAGE_KEY);
        }, 100);
      } else if (broadcastChannel) {
        // Use BroadcastChannel
        broadcastChannel.postMessage(message);
      } else {
        console.warn("No broadcast method available");
        return false;
      }

      // Update performance metrics
      performanceMetrics.messagesSent++;

      if (enableLogging) {
        console.log(
          "Broadcast message sent:",
          message,
          fallbackMode ? "(fallback)" : "(broadcast)"
        );
      }

      return true;
    } catch (error) {
      console.error("Failed to send broadcast message:", error);
      return false;
    }
  };

  // Register listener for specific message type
  const onBroadcast = (
    type: keyof typeof MESSAGE_TYPES,
    callback: (message: BroadcastMessage) => void
  ) => {
    const messageType = MESSAGE_TYPES[type];

    if (!listeners.has(messageType)) {
      listeners.set(messageType, []);
    }

    listeners.get(messageType)!.push(callback);

    // Return unsubscribe function
    return () => {
      const typeListeners = listeners.get(messageType);
      if (typeListeners) {
        const index = typeListeners.indexOf(callback);
        if (index > -1) {
          typeListeners.splice(index, 1);
        }
      }
    };
  };

  // Broadcast session expired event
  const broadcastSessionExpired = (reason?: string) => {
    return broadcast("SESSION_EXPIRED", { reason });
  };

  // Broadcast logout event
  const broadcastLogout = (reason?: string) => {
    return broadcast("LOGOUT", { reason });
  };

  // Broadcast session refresh event
  const broadcastSessionRefresh = () => {
    return broadcast("SESSION_REFRESH");
  };

  // Broadcast login success event
  const broadcastLoginSuccess = (authData?: any) => {
    return broadcast("LOGIN_SUCCESS", { authData });
  };

  // Broadcast auth state sync event
  const broadcastAuthStateSync = (authState?: any) => {
    return broadcast("AUTH_STATE_SYNC", { authState });
  };

  // Listen for session expired events
  const onSessionExpired = (callback: (message: BroadcastMessage) => void) => {
    return onBroadcast("SESSION_EXPIRED", callback);
  };

  // Listen for logout events
  const onLogout = (callback: (message: BroadcastMessage) => void) => {
    return onBroadcast("LOGOUT", callback);
  };

  // Listen for session refresh events
  const onSessionRefresh = (callback: (message: BroadcastMessage) => void) => {
    return onBroadcast("SESSION_REFRESH", callback);
  };

  // Listen for login success events
  const onLoginSuccess = (callback: (message: BroadcastMessage) => void) => {
    return onBroadcast("LOGIN_SUCCESS", callback);
  };

  // Listen for auth state sync events
  const onAuthStateSync = (callback: (message: BroadcastMessage) => void) => {
    return onBroadcast("AUTH_STATE_SYNC", callback);
  };

  // Reset session expired modal state
  const resetSessionExpiredModal = () => {
    isSessionExpiredModalShown.value = false;
  };

  // Check if session expired modal is already shown
  const getSessionExpiredModalState = () => {
    return isSessionExpiredModalShown.value;
  };

  // Set session expired modal state
  const setSessionExpiredModalState = (shown: boolean) => {
    isSessionExpiredModalShown.value = shown;
  };

  // Check for and handle deferred session expired messages
  const handleDeferredMessages = () => {
    const deferredMessage = sessionStorage.getItem(
      "dms_deferred_session_expired"
    );
    if (deferredMessage) {
      try {
        const message = JSON.parse(deferredMessage);
        sessionStorage.removeItem("dms_deferred_session_expired");

        // Process the deferred message
        handleBroadcastMessage(message);
      } catch (error) {
        console.error(
          "Failed to process deferred session expired message:",
          error
        );
        sessionStorage.removeItem("dms_deferred_session_expired");
      }
    }
  };

  // Get performance metrics
  const getPerformanceMetrics = () => {
    return {
      ...performanceMetrics,
      messageHistorySize: messageHistory.size,
      listenerCount: Array.from(listeners.values()).reduce(
        (sum, arr) => sum + arr.length,
        0
      ),
      uptime: Date.now() - performanceMetrics.lastCleanup,
    };
  };

  // Reset performance metrics
  const resetPerformanceMetrics = () => {
    performanceMetrics.messagesSent = 0;
    performanceMetrics.messagesReceived = 0;
    performanceMetrics.messagesDropped = 0;
    performanceMetrics.averageProcessingTime = 0;
    performanceMetrics.processingTimes = [];
    performanceMetrics.lastCleanup = Date.now();
  };

  // Enhanced cleanup function
  const cleanup = () => {
    if (broadcastChannel) {
      broadcastChannel.close();
      broadcastChannel = null;
    }

    // Clear all listeners
    listeners.clear();

    // Clear message history
    messageHistory.clear();

    // Reset performance metrics
    resetPerformanceMetrics();

    // Remove storage event listener if in fallback mode
    if (fallbackMode) {
      window.removeEventListener("storage", () => {});
    }

    if (enableLogging) {
      console.log("BroadcastChannel cleanup completed");
    }
  };

  // Auto cleanup on unmount
  onUnmounted(() => {
    cleanup();
  });

  return {
    // Core functions
    initializeBroadcast,
    broadcast,
    onBroadcast,
    cleanup,

    // Specific event functions
    broadcastSessionExpired,
    broadcastLogout,
    broadcastSessionRefresh,
    broadcastLoginSuccess,
    broadcastAuthStateSync,
    onSessionExpired,
    onLogout,
    onSessionRefresh,
    onLoginSuccess,
    onAuthStateSync,

    // Modal state management
    resetSessionExpiredModal,
    getSessionExpiredModalState,
    setSessionExpiredModalState,
    handleDeferredMessages,

    // Performance monitoring
    getPerformanceMetrics,
    resetPerformanceMetrics,

    // State
    isSessionExpiredModalShown: readonly(isSessionExpiredModalShown),

    // Constants
    MESSAGE_TYPES,
  };
}
