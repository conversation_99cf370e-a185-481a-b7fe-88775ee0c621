# Cross-Tab Broadcast System Documentation

## Overview

This comprehensive cross-tab synchronization system uses the BroadcastChannel API to handle login and logout states across multiple browser tabs in real-time. It provides seamless user experience by automatically synchronizing authentication states and displaying appropriate notifications when sessions expire.

## Features

### ✅ Core Functionality
- **Real-time cross-tab communication** using BroadcastChannel API
- **Session expiry notifications** when user logs out in another tab
- **Login state synchronization** when user logs in successfully
- **Automatic modal display** with countdown and redirect options
- **Browser compatibility fallback** using localStorage events

### ✅ User Experience
- **Vietnamese language support** with contextual messages
- **Auto-redirect countdown** (configurable, default 10 seconds)
- **Current page context preservation** when redirecting to login
- **Accessibility support** with keyboard navigation
- **Mobile-responsive design** following existing UI patterns

### ✅ Edge Cases Handled
- **Duplicate notification prevention** across tabs
- **Background tab handling** (deferred notifications)
- **Message deduplication** to prevent processing same event multiple times
- **Memory leak prevention** with automatic cleanup
- **Performance monitoring** and optimization

## Architecture

### Components

1. **`useCrossTabBroadcast.ts`** - Core composable for cross-tab communication
2. **`ModalSessionExpired.vue`** - Session expired modal component
3. **`session-monitor.client.ts`** - Global plugin for session monitoring
4. **`useAuth.ts`** - Enhanced authentication composable
5. **`testCrossTabBroadcast.ts`** - Comprehensive testing utilities

### Message Types

```typescript
interface BroadcastMessage {
  type: "SESSION_EXPIRED" | "LOGOUT" | "LOGIN_SUCCESS" | "AUTH_STATE_SYNC" | "SESSION_REFRESH";
  timestamp: number;
  tabId?: string;
  data?: any;
}
```

## Usage

### Basic Implementation

The system is automatically initialized when the application starts. No manual setup required.

### Manual Testing

```javascript
// Initialize test suite
testCrossTabBroadcast();

// Run all tests
window.testCrossTab.runAllTests();

// Test individual functions
window.testCrossTab.testSessionExpired();
window.testCrossTab.testLogout();
window.testCrossTab.testLoginSuccess();

// Performance testing
window.testCrossTab.performanceTest();
window.testCrossTab.stressTest();

// Get performance metrics
window.testCrossTab.getResults();

// Cleanup
window.testCrossTab.cleanup();
```

### Real-world Testing

1. **Open multiple tabs** of the application
2. **Login in one tab** - other tabs should sync authentication state
3. **Logout from any tab** - all other tabs should show session expired modal
4. **Test background tabs** - switch away, logout from another tab, switch back
5. **Test mobile responsiveness** - verify modal works on mobile devices

## Configuration

### Modal Configuration

```vue
<ModalSessionExpired
  :is-open="isOpen"
  :reason="reason"
  :allow-close="false"
  :allow-dismiss="false"
  :auto-redirect="true"
  :auto-redirect-delay="10"
  :show-details="false"
/>
```

### Broadcast Options

```typescript
const broadcast = useCrossTabBroadcast({
  channelName: 'custom-channel-name',
  enableLogging: true // Enable for development
});
```

## Performance Monitoring

The system includes built-in performance monitoring:

```javascript
const metrics = broadcast.getPerformanceMetrics();
console.log({
  messagesSent: metrics.messagesSent,
  messagesReceived: metrics.messagesReceived,
  messagesDropped: metrics.messagesDropped,
  averageProcessingTime: metrics.averageProcessingTime,
  messageHistorySize: metrics.messageHistorySize,
  listenerCount: metrics.listenerCount,
  uptime: metrics.uptime
});
```

## Browser Compatibility

### Supported Browsers
- **Chrome 54+**
- **Firefox 38+**
- **Safari 15.4+**
- **Edge 79+**

### Fallback Support
For browsers that don't support BroadcastChannel API, the system automatically falls back to localStorage events.

## Security Considerations

1. **Message validation** - All messages are validated before processing
2. **Tab isolation** - Messages include tab IDs to prevent self-processing
3. **Timestamp validation** - Old messages are automatically discarded
4. **Deduplication** - Prevents processing duplicate messages
5. **Session security** - Automatic logout on session expiry

## Troubleshooting

### Common Issues

**Modal not appearing:**
- Check if BroadcastChannel is supported
- Verify session monitor plugin is loaded
- Check browser console for errors

**Performance issues:**
- Monitor performance metrics
- Check message history size
- Verify cleanup is working properly

**Fallback mode not working:**
- Ensure localStorage is available
- Check for storage event listeners
- Verify fallback initialization

### Debug Mode

Enable logging for debugging:

```typescript
const broadcast = useCrossTabBroadcast({ enableLogging: true });
```

## API Reference

### Core Functions

- `initializeBroadcast()` - Initialize broadcast system
- `broadcast(type, data)` - Send message to other tabs
- `onBroadcast(type, callback)` - Listen for specific message types
- `cleanup()` - Clean up resources and listeners

### Authentication Functions

- `broadcastLoginSuccess(authData)` - Broadcast successful login
- `broadcastLogout(reason)` - Broadcast logout event
- `broadcastSessionExpired(reason)` - Broadcast session expiry
- `handleLoginSuccess(loginData)` - Handle login from other tabs
- `handleSessionExpired(reason)` - Handle session expiry from other tabs

### Performance Functions

- `getPerformanceMetrics()` - Get current performance metrics
- `resetPerformanceMetrics()` - Reset performance counters

## Best Practices

1. **Always cleanup** listeners when components unmount
2. **Use performance monitoring** in development
3. **Test across multiple tabs** regularly
4. **Monitor memory usage** for long-running applications
5. **Handle edge cases** like network interruptions
6. **Provide user feedback** for all state changes

## Contributing

When contributing to this system:

1. **Test thoroughly** across multiple browsers and tabs
2. **Update documentation** for any API changes
3. **Add performance tests** for new features
4. **Follow existing code patterns** and naming conventions
5. **Ensure accessibility** compliance for UI components

## License

This cross-tab broadcast system is part of the DMS application and follows the same licensing terms.
